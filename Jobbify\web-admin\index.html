<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jobbify Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: #007AFF;
            margin-bottom: 8px;
        }

        .header p {
            color: #666;
        }

        .auth-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .auth-section input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .auth-section button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .auth-section button:hover {
            background: #0056b3;
        }

        .filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-size: 12px;
            font-weight: 500;
            color: #666;
        }

        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .applications-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .applications-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .bulk-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .bulk-actions select, .bulk-actions button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .bulk-actions button {
            background: #007AFF;
            color: white;
            border: 1px solid #007AFF;
            cursor: pointer;
        }

        .bulk-actions button:hover {
            background: #0056b3;
        }

        .bulk-actions button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .applications-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .application-item {
            padding: 16px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .application-item:hover {
            background: #f9f9f9;
        }

        .application-checkbox {
            width: 18px;
            height: 18px;
        }

        .application-info {
            flex: 1;
        }

        .job-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .job-company {
            color: #666;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .applicant-info {
            color: #888;
            font-size: 12px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            color: white;
            text-transform: uppercase;
        }

        .status-applying { background: #FFA500; }
        .status-applied { background: #4CAF50; }
        .status-failed { background: #F44336; }
        .status-interviewing { background: #2196F3; }
        .status-offered { background: #9C27B0; }
        .status-rejected { background: #795548; }
        .status-accepted { background: #4CAF50; }
        .status-withdrawn { background: #607D8B; }

        .application-date {
            color: #888;
            font-size: 12px;
            text-align: right;
            min-width: 120px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .applications-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .bulk-actions {
                justify-content: space-between;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Jobbify Admin Dashboard</h1>
            <p>Manage application statuses for all users</p>
        </div>

        <div class="auth-section" id="authSection">
            <h3>Admin Authentication</h3>
            <input type="text" id="adminUserId" placeholder="Enter your admin user ID" />
            <button onclick="authenticate()">Authenticate</button>
        </div>

        <div id="mainContent" class="hidden">
            <div class="filters">
                <div class="filter-row">
                    <div class="filter-group">
                        <label>Status Filter</label>
                        <select id="statusFilter" onchange="loadApplications()">
                            <option value="all">All Statuses</option>
                            <option value="applying">Applying</option>
                            <option value="applied">Applied</option>
                            <option value="failed">Failed</option>
                            <option value="interviewing">Interviewing</option>
                            <option value="offered">Offered</option>
                            <option value="rejected">Rejected</option>
                            <option value="accepted">Accepted</option>
                            <option value="withdrawn">Withdrawn</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>&nbsp;</label>
                        <button onclick="loadApplications()">Refresh</button>
                    </div>
                </div>
            </div>

            <div class="applications-section">
                <div class="applications-header">
                    <h3>Applications (<span id="applicationCount">0</span>)</h3>
                    <div class="bulk-actions">
                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" />
                        <label for="selectAll">Select All</label>
                        <select id="bulkStatus">
                            <option value="">Change Status To...</option>
                            <option value="applying">Applying</option>
                            <option value="applied">Applied</option>
                            <option value="failed">Failed</option>
                            <option value="interviewing">Interviewing</option>
                            <option value="offered">Offered</option>
                            <option value="rejected">Rejected</option>
                            <option value="accepted">Accepted</option>
                            <option value="withdrawn">Withdrawn</option>
                        </select>
                        <button id="bulkUpdateBtn" onclick="bulkUpdateStatus()" disabled>
                            Update Selected
                        </button>
                    </div>
                </div>
                <div class="applications-list" id="applicationsList">
                    <div class="loading">Loading applications...</div>
                </div>
            </div>
        </div>

        <div id="messageArea"></div>
    </div>

    <script>
        let currentAdminUserId = '';
        let applications = [];
        let selectedApplications = new Set();

        // Supabase configuration
        const SUPABASE_URL = 'https://ubueawlkwlvgzxcslats.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVidWVhd2xrd2x2Z3p4Y3NsYXRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU0MzgzMTIsImV4cCI6MjA2MTAxNDMxMn0._YMssGKgq17C5XMkPnN5rq5Zhu_u4WsvNiveYIPd4lg';

        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('messageArea');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            messageArea.innerHTML = `<div class="${className}">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 5000);
        }

        async function authenticate() {
            const userId = document.getElementById('adminUserId').value.trim();
            if (!userId) {
                showMessage('Please enter your admin user ID', 'error');
                return;
            }

            try {
                // Verify admin access by trying to fetch applications
                const response = await fetch(`${SUPABASE_URL}/rest/v1/profiles?id=eq.${userId}&select=user_type`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (data && data.length > 0 && data[0].user_type === 'admin') {
                    currentAdminUserId = userId;
                    document.getElementById('authSection').classList.add('hidden');
                    document.getElementById('mainContent').classList.remove('hidden');
                    showMessage('Authentication successful!', 'success');
                    loadApplications();
                } else {
                    showMessage('Invalid admin credentials or insufficient privileges', 'error');
                }
            } catch (error) {
                console.error('Authentication error:', error);
                showMessage('Authentication failed. Please check your credentials.', 'error');
            }
        }

        async function loadApplications() {
            const statusFilter = document.getElementById('statusFilter').value;
            const applicationsList = document.getElementById('applicationsList');
            
            applicationsList.innerHTML = '<div class="loading">Loading applications...</div>';

            try {
                let url = `${SUPABASE_URL}/rest/v1/matches?select=*,profiles:profile_id(id,name,email),jobs:job_id(id,title,company,location,logo_url)&order=created_at.desc`;
                
                if (statusFilter !== 'all') {
                    url += `&status=eq.${statusFilter}`;
                }

                const response = await fetch(url, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to fetch applications');
                }

                applications = await response.json();
                renderApplications();
                
            } catch (error) {
                console.error('Error loading applications:', error);
                applicationsList.innerHTML = '<div class="error">Failed to load applications. Please try again.</div>';
                showMessage('Failed to load applications', 'error');
            }
        }

        function renderApplications() {
            const applicationsList = document.getElementById('applicationsList');
            const applicationCount = document.getElementById('applicationCount');
            
            applicationCount.textContent = applications.length;

            if (applications.length === 0) {
                applicationsList.innerHTML = '<div class="loading">No applications found</div>';
                return;
            }

            const html = applications.map(app => {
                const isSelected = selectedApplications.has(app.id.toString());
                const statusClass = `status-${app.status}`;
                const jobTitle = app.job_title || app.jobs?.title || 'Unknown Job';
                const company = app.job_company || app.jobs?.company || 'Unknown Company';
                const applicantName = app.profiles?.name || 'Unknown User';
                const applicantEmail = app.profiles?.email || '';
                const createdDate = new Date(app.created_at).toLocaleDateString();
                const updatedDate = app.updated_at !== app.created_at ? new Date(app.updated_at).toLocaleDateString() : '';

                return `
                    <div class="application-item">
                        <input type="checkbox" class="application-checkbox" 
                               ${isSelected ? 'checked' : ''} 
                               onchange="toggleApplicationSelection('${app.id}')" />
                        <div class="application-info">
                            <div class="job-title">${jobTitle}</div>
                            <div class="job-company">${company}</div>
                            <div class="applicant-info">${applicantName} (${applicantEmail})</div>
                        </div>
                        <div class="status-badge ${statusClass}">${app.status}</div>
                        <div class="application-date">
                            Applied: ${createdDate}
                            ${updatedDate ? `<br>Updated: ${updatedDate}` : ''}
                        </div>
                    </div>
                `;
            }).join('');

            applicationsList.innerHTML = html;
            updateBulkActions();
        }

        function toggleApplicationSelection(appId) {
            if (selectedApplications.has(appId)) {
                selectedApplications.delete(appId);
            } else {
                selectedApplications.add(appId);
            }
            updateBulkActions();
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            if (selectAll.checked) {
                applications.forEach(app => selectedApplications.add(app.id.toString()));
            } else {
                selectedApplications.clear();
            }
            renderApplications();
        }

        function updateBulkActions() {
            const bulkUpdateBtn = document.getElementById('bulkUpdateBtn');
            const bulkStatus = document.getElementById('bulkStatus');
            const selectAll = document.getElementById('selectAll');
            
            const hasSelection = selectedApplications.size > 0;
            const hasStatus = bulkStatus.value !== '';
            
            bulkUpdateBtn.disabled = !hasSelection || !hasStatus;
            selectAll.checked = selectedApplications.size === applications.length && applications.length > 0;
            selectAll.indeterminate = selectedApplications.size > 0 && selectedApplications.size < applications.length;
        }

        async function bulkUpdateStatus() {
            const bulkStatus = document.getElementById('bulkStatus').value;
            if (!bulkStatus || selectedApplications.size === 0) return;

            const confirmed = confirm(`Are you sure you want to update ${selectedApplications.size} application(s) to "${bulkStatus}"?`);
            if (!confirmed) return;

            try {
                const applicationIds = Array.from(selectedApplications);
                
                const response = await fetch(`${SUPABASE_URL}/rest/v1/matches`, {
                    method: 'PATCH',
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json',
                        'Prefer': 'return=minimal'
                    },
                    body: JSON.stringify({
                        status: bulkStatus,
                        updated_at: new Date().toISOString()
                    })
                });

                if (response.ok) {
                    showMessage(`Successfully updated ${selectedApplications.size} application(s)`, 'success');
                    selectedApplications.clear();
                    document.getElementById('bulkStatus').value = '';
                    loadApplications();
                } else {
                    throw new Error('Update failed');
                }
            } catch (error) {
                console.error('Error updating applications:', error);
                showMessage('Failed to update applications', 'error');
            }
        }

        // Initialize bulk actions update
        document.getElementById('bulkStatus').addEventListener('change', updateBulkActions);
    </script>
</body>
</html>
