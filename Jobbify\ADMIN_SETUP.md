# Jobbify Admin Application Management System

This system allows developers to view and manage all user applications through both a React Native admin interface and a web-based dashboard.

## Features

- ✅ View all applications across all users
- ✅ Filter applications by status (applying, applied, failed, etc.)
- ✅ Update individual application statuses
- ✅ Bulk update multiple applications at once
- ✅ Audit trail for all status changes
- ✅ Admin authentication and authorization
- ✅ Both mobile (React Native) and web interfaces

## Setup Instructions

### 1. Database Schema Update

First, you need to update your Supabase database schema to support the new application management features.

#### Option A: Run the automated script
```bash
cd Jobbify
npx ts-node scripts/updateDatabaseSchema.ts
```

#### Option B: Manual SQL execution
Run the following SQL in your Supabase SQL editor:

```sql
-- Update matches table to include new status options and fields
ALTER TABLE matches DROP CONSTRAINT IF EXISTS matches_status_check;
ALTER TABLE matches ADD CONSTRAINT matches_status_check 
  CHECK (status IN ('pending', 'accepted', 'rejected', 'archived', 'applying', 'applied', 'failed'));

-- Add new columns if they don't exist
ALTER TABLE matches ADD COLUMN IF NOT EXISTS cover_letter TEXT;
ALTER TABLE matches ADD COLUMN IF NOT EXISTS application_email TEXT;
ALTER TABLE matches ADD COLUMN IF NOT EXISTS admin_notes TEXT;
ALTER TABLE matches ADD COLUMN IF NOT EXISTS metadata JSONB;

-- Create application_status_changes table for audit trail
CREATE TABLE IF NOT EXISTS application_status_changes (
  id SERIAL PRIMARY KEY,
  application_id INTEGER REFERENCES matches(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  job_id TEXT NOT NULL,
  old_status TEXT,
  new_status TEXT,
  changed_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  change_reason TEXT,
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS and create policies
ALTER TABLE matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE application_status_changes ENABLE ROW LEVEL SECURITY;

-- Admin policies for matches table
CREATE POLICY "Admins can view all matches"
  ON matches FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'admin'
    )
  );

CREATE POLICY "Admins can update all matches"
  ON matches FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'admin'
    )
  );

-- Policies for application_status_changes table
CREATE POLICY "Admins can view all status changes"
  ON application_status_changes FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'admin'
    )
  );

CREATE POLICY "Admins can insert status changes"
  ON application_status_changes FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.user_type = 'admin'
    )
  );
```

### 2. Create Admin User

To create an admin user, update a user's profile in Supabase:

```sql
UPDATE profiles 
SET user_type = 'admin' 
WHERE id = 'YOUR_USER_ID_HERE';
```

Or use the helper function:
```typescript
import { createAdminUser } from './scripts/updateDatabaseSchema';
await createAdminUser('YOUR_USER_ID_HERE');
```

### 3. Access the Admin Interfaces

#### React Native Admin Interface
Navigate to the admin section in your app:
```
/admin/dashboard
```

This interface provides:
- Full application list with filtering
- Individual and bulk status updates
- Real-time data refresh
- Mobile-optimized UI

#### Web Admin Interface
Open the web admin dashboard in your browser:
```
file:///path/to/Jobbify/web-admin/index.html
```

Or serve it through a web server:
```bash
cd Jobbify/web-admin
python -m http.server 8080
# Then open http://localhost:8080
```

The web interface provides:
- Simple authentication with admin user ID
- Application filtering and management
- Bulk operations
- Responsive design for desktop and mobile

## Usage

### Status Options
- **applying**: Application is being processed
- **applied**: Application has been successfully submitted
- **failed**: Application submission failed
- **interviewing**: User is in interview process
- **offered**: User received a job offer
- **rejected**: Application was rejected
- **accepted**: User accepted the job offer
- **withdrawn**: User withdrew their application

### Admin Functions

#### View Applications
```typescript
import { getAdminApplications } from '@/services/adminService';

const result = await getAdminApplications(adminUserId);
if (result.success) {
  console.log('Applications:', result.data);
}
```

#### Update Single Application
```typescript
import { adminUpdateApplicationStatus } from '@/services/adminService';

const result = await adminUpdateApplicationStatus(
  adminUserId,
  applicationId,
  'applied',
  'Application submitted successfully'
);
```

#### Bulk Update Applications
```typescript
import { adminBulkUpdateApplicationStatus } from '@/services/adminService';

const result = await adminBulkUpdateApplicationStatus(
  adminUserId,
  ['app1', 'app2', 'app3'],
  'applied',
  'Bulk update - applications processed'
);
```

#### View Status Change History
```typescript
import { getApplicationStatusHistory } from '@/services/adminService';

const result = await getApplicationStatusHistory(adminUserId, applicationId);
if (result.success) {
  console.log('Status history:', result.data);
}
```

## Security

- All admin functions require user authentication
- Admin access is verified through the `user_type` field in profiles table
- Row Level Security (RLS) policies ensure data isolation
- All status changes are logged for audit purposes
- Admin actions are tracked with timestamps and user IDs

## API Endpoints

### GET /api/admin/applications
Get all applications with filtering options.

Query parameters:
- `admin_user_id`: Required admin user ID
- `status`: Filter by status (optional)
- `limit`: Number of results (default: 50)
- `offset`: Pagination offset (default: 0)

### PUT /api/admin/applications
Update application status(es).

Body:
```json
{
  "admin_user_id": "admin-uuid",
  "application_id": "123", // For single update
  "application_ids": ["123", "456"], // For bulk update
  "status": "applied",
  "notes": "Optional notes"
}
```

## Troubleshooting

### Common Issues

1. **"Unauthorized: Admin access required"**
   - Ensure the user has `user_type = 'admin'` in the profiles table
   - Verify the admin user ID is correct

2. **"Application not found"**
   - Check that the application ID exists in the matches table
   - Ensure RLS policies are properly configured

3. **Database connection errors**
   - Verify Supabase URL and API key are correct
   - Check that the database schema has been updated

4. **Web interface not loading applications**
   - Ensure you're using the correct admin user ID
   - Check browser console for network errors
   - Verify CORS settings in Supabase

### Support

For issues or questions, check:
1. Supabase dashboard for database errors
2. Browser console for client-side errors
3. Application logs for server-side errors
4. RLS policies in Supabase for permission issues
