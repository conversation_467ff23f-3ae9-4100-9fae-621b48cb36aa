import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// GET /api/admin/applications - Get all applications for admin
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const adminUserId = searchParams.get('admin_user_id');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    if (!adminUserId) {
      return NextResponse.json(
        { error: 'Admin user ID is required' },
        { status: 400 }
      );
    }

    // Verify admin access
    const { data: adminProfile, error: adminError } = await supabase
      .from('profiles')
      .select('user_type')
      .eq('id', adminUserId)
      .single();

    if (adminError || !adminProfile || adminProfile.user_type !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized: Admin access required' },
        { status: 403 }
      );
    }

    // Build query
    let query = supabase
      .from('matches')
      .select(`
        *,
        profiles:profile_id (
          id,
          name,
          email
        ),
        jobs:job_id (
          id,
          title,
          company,
          location,
          logo_url
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Filter by status if provided
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    const { data: applications, error } = await query;

    if (error) {
      console.error('Error fetching applications:', error);
      return NextResponse.json(
        { error: 'Failed to fetch applications' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('matches')
      .select('*', { count: 'exact', head: true });

    if (status && status !== 'all') {
      countQuery = countQuery.eq('status', status);
    }

    const { count, error: countError } = await countQuery;

    return NextResponse.json({
      success: true,
      data: applications,
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    });

  } catch (error) {
    console.error('Error in admin applications API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/applications - Update application status
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      admin_user_id, 
      application_id, 
      application_ids, 
      status, 
      notes 
    } = body;

    if (!admin_user_id) {
      return NextResponse.json(
        { error: 'Admin user ID is required' },
        { status: 400 }
      );
    }

    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    if (!application_id && !application_ids) {
      return NextResponse.json(
        { error: 'Application ID or IDs are required' },
        { status: 400 }
      );
    }

    // Verify admin access
    const { data: adminProfile, error: adminError } = await supabase
      .from('profiles')
      .select('user_type')
      .eq('id', admin_user_id)
      .single();

    if (adminError || !adminProfile || adminProfile.user_type !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized: Admin access required' },
        { status: 403 }
      );
    }

    // Validate status
    const validStatuses = ['applying', 'applied', 'failed', 'interviewing', 'offered', 'rejected', 'accepted', 'withdrawn'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: `Invalid status. Must be one of: ${validStatuses.join(', ')}` },
        { status: 400 }
      );
    }

    const idsToUpdate = application_ids || [application_id];
    const results = {
      successful: [] as any[],
      failed: [] as any[],
      total: idsToUpdate.length
    };

    // Process each application
    for (const appId of idsToUpdate) {
      try {
        // Get current application data
        const { data: currentApp, error: fetchError } = await supabase
          .from('matches')
          .select('*')
          .eq('id', appId)
          .single();

        if (fetchError || !currentApp) {
          results.failed.push({
            id: appId,
            error: 'Application not found'
          });
          continue;
        }

        const oldStatus = currentApp.status;

        // Update application status
        const updatePayload: any = {
          status,
          updated_at: new Date().toISOString()
        };

        if (notes) {
          updatePayload.admin_notes = notes;
        }

        const { data: updatedApp, error: updateError } = await supabase
          .from('matches')
          .update(updatePayload)
          .eq('id', appId)
          .select()
          .single();

        if (updateError) {
          results.failed.push({
            id: appId,
            error: updateError.message
          });
          continue;
        }

        // Log the status change
        const auditLog = {
          application_id: parseInt(appId),
          user_id: currentApp.profile_id,
          job_id: currentApp.job_id,
          old_status: oldStatus,
          new_status: status,
          changed_by: admin_user_id,
          change_reason: notes || `Status changed from ${oldStatus} to ${status}`,
          changed_at: new Date().toISOString()
        };

        try {
          await supabase
            .from('application_status_changes')
            .insert(auditLog);
        } catch (logError) {
          console.warn('Could not log status change:', logError);
          // Don't fail the operation if logging fails
        }

        results.successful.push({
          id: appId,
          old_status: oldStatus,
          new_status: status,
          data: updatedApp
        });

      } catch (error) {
        results.failed.push({
          id: appId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: `Update completed. ${results.successful.length} successful, ${results.failed.length} failed`,
      results
    });

  } catch (error) {
    console.error('Error in admin applications update API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
