import { supabase } from '../lib/supabase';

/**
 * <PERSON><PERSON><PERSON> to update the database schema for application status management
 * Run this script to apply the necessary database changes
 */

export async function updateDatabaseSchema() {
  console.log('🔄 Starting database schema update...');

  try {
    // 1. Update matches table to include new status options and fields
    console.log('📝 Updating matches table...');
    
    const updateMatchesTable = `
      -- Add new status options to matches table
      ALTER TABLE matches DROP CONSTRAINT IF EXISTS matches_status_check;
      ALTER TABLE matches ADD CONSTRAINT matches_status_check 
        CHECK (status IN ('pending', 'accepted', 'rejected', 'archived', 'applying', 'applied', 'failed'));
      
      -- Add new columns if they don't exist
      ALTER TABLE matches ADD COLUMN IF NOT EXISTS cover_letter TEXT;
      ALTER TABLE matches ADD COLUMN IF NOT EXISTS application_email TEXT;
      ALTER TABLE matches ADD COLUMN IF NOT EXISTS admin_notes TEXT;
      ALTER TABLE matches ADD COLUMN IF NOT EXISTS metadata JSONB;
    `;

    const { error: matchesError } = await supabase.rpc('exec_sql', { 
      sql: updateMatchesTable 
    });

    if (matchesError) {
      console.error('❌ Error updating matches table:', matchesError);
    } else {
      console.log('✅ Matches table updated successfully');
    }

    // 2. Create application_status_changes table
    console.log('📝 Creating application_status_changes table...');
    
    const createStatusChangesTable = `
      CREATE TABLE IF NOT EXISTS application_status_changes (
        id SERIAL PRIMARY KEY,
        application_id INTEGER REFERENCES matches(id) ON DELETE CASCADE,
        user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
        job_id TEXT NOT NULL,
        old_status TEXT,
        new_status TEXT,
        changed_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
        change_reason TEXT,
        changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    const { error: statusChangesError } = await supabase.rpc('exec_sql', { 
      sql: createStatusChangesTable 
    });

    if (statusChangesError) {
      console.error('❌ Error creating application_status_changes table:', statusChangesError);
    } else {
      console.log('✅ Application status changes table created successfully');
    }

    // 3. Enable RLS and create policies for matches table
    console.log('📝 Setting up RLS policies for matches table...');
    
    const matchesPolicies = `
      -- Enable RLS on matches table
      ALTER TABLE matches ENABLE ROW LEVEL SECURITY;
      
      -- Drop existing policies if they exist
      DROP POLICY IF EXISTS "Users can view their own matches" ON matches;
      DROP POLICY IF EXISTS "Users can insert their own matches" ON matches;
      DROP POLICY IF EXISTS "Users can update their own matches" ON matches;
      DROP POLICY IF EXISTS "Users can delete their own matches" ON matches;
      DROP POLICY IF EXISTS "Admins can view all matches" ON matches;
      DROP POLICY IF EXISTS "Admins can update all matches" ON matches;
      
      -- Create user policies
      CREATE POLICY "Users can view their own matches"
        ON matches FOR SELECT
        USING (auth.uid() = profile_id);

      CREATE POLICY "Users can insert their own matches"
        ON matches FOR INSERT
        WITH CHECK (auth.uid() = profile_id);

      CREATE POLICY "Users can update their own matches"
        ON matches FOR UPDATE
        USING (auth.uid() = profile_id);

      CREATE POLICY "Users can delete their own matches"
        ON matches FOR DELETE
        USING (auth.uid() = profile_id);

      -- Create admin policies
      CREATE POLICY "Admins can view all matches"
        ON matches FOR SELECT
        USING (
          EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.user_type = 'admin'
          )
        );

      CREATE POLICY "Admins can update all matches"
        ON matches FOR UPDATE
        USING (
          EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.user_type = 'admin'
          )
        );
    `;

    const { error: matchesPoliciesError } = await supabase.rpc('exec_sql', { 
      sql: matchesPolicies 
    });

    if (matchesPoliciesError) {
      console.error('❌ Error setting up matches table policies:', matchesPoliciesError);
    } else {
      console.log('✅ Matches table policies created successfully');
    }

    // 4. Enable RLS and create policies for application_status_changes table
    console.log('📝 Setting up RLS policies for application_status_changes table...');
    
    const statusChangesPolicies = `
      -- Enable RLS on application_status_changes table
      ALTER TABLE application_status_changes ENABLE ROW LEVEL SECURITY;
      
      -- Drop existing policies if they exist
      DROP POLICY IF EXISTS "Users can view their own status changes" ON application_status_changes;
      DROP POLICY IF EXISTS "Admins can view all status changes" ON application_status_changes;
      DROP POLICY IF EXISTS "Admins can insert status changes" ON application_status_changes;
      
      -- Create policies
      CREATE POLICY "Users can view their own status changes"
        ON application_status_changes FOR SELECT
        USING (auth.uid() = user_id);

      CREATE POLICY "Admins can view all status changes"
        ON application_status_changes FOR SELECT
        USING (
          EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.user_type = 'admin'
          )
        );

      CREATE POLICY "Admins can insert status changes"
        ON application_status_changes FOR INSERT
        WITH CHECK (
          EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.user_type = 'admin'
          )
        );
    `;

    const { error: statusChangesPoliciesError } = await supabase.rpc('exec_sql', { 
      sql: statusChangesPolicies 
    });

    if (statusChangesPoliciesError) {
      console.error('❌ Error setting up status changes table policies:', statusChangesPoliciesError);
    } else {
      console.log('✅ Status changes table policies created successfully');
    }

    // 5. Create indexes for better performance
    console.log('📝 Creating indexes...');
    
    const createIndexes = `
      -- Create indexes for better query performance
      CREATE INDEX IF NOT EXISTS idx_matches_profile_id ON matches(profile_id);
      CREATE INDEX IF NOT EXISTS idx_matches_status ON matches(status);
      CREATE INDEX IF NOT EXISTS idx_matches_created_at ON matches(created_at);
      CREATE INDEX IF NOT EXISTS idx_application_status_changes_application_id ON application_status_changes(application_id);
      CREATE INDEX IF NOT EXISTS idx_application_status_changes_user_id ON application_status_changes(user_id);
      CREATE INDEX IF NOT EXISTS idx_application_status_changes_changed_at ON application_status_changes(changed_at);
    `;

    const { error: indexesError } = await supabase.rpc('exec_sql', { 
      sql: createIndexes 
    });

    if (indexesError) {
      console.error('❌ Error creating indexes:', indexesError);
    } else {
      console.log('✅ Indexes created successfully');
    }

    console.log('🎉 Database schema update completed successfully!');
    
    return {
      success: true,
      message: 'Database schema updated successfully'
    };

  } catch (error) {
    console.error('💥 Error updating database schema:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Function to create an admin user (for testing)
export async function createAdminUser(userId: string) {
  try {
    console.log('👤 Creating admin user...');
    
    const { data, error } = await supabase
      .from('profiles')
      .update({ user_type: 'admin' })
      .eq('id', userId)
      .select();

    if (error) {
      console.error('❌ Error creating admin user:', error);
      return { success: false, error: error.message };
    }

    console.log('✅ Admin user created successfully:', data);
    return { success: true, data };
  } catch (error) {
    console.error('💥 Error creating admin user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Run the schema update if this file is executed directly
if (require.main === module) {
  updateDatabaseSchema()
    .then(result => {
      if (result.success) {
        console.log('✅ Schema update completed successfully');
        process.exit(0);
      } else {
        console.error('❌ Schema update failed:', result.error);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Unexpected error:', error);
      process.exit(1);
    });
}
