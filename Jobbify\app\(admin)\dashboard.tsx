import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome } from '@expo/vector-icons';
import { useAuth } from '@/hooks/useAuth';
import { 
  getAdminApplications, 
  adminUpdateApplicationStatus, 
  adminBulkUpdateApplicationStatus,
  getApplicationStatusHistory 
} from '@/services/adminService';

interface Application {
  id: string;
  profile_id: string;
  job_id: string;
  job_title: string;
  job_company: string;
  status: string;
  created_at: string;
  updated_at: string;
  admin_notes?: string;
  profiles?: {
    id: string;
    name: string;
    email: string;
  };
  jobs?: {
    id: string;
    title: string;
    company: string;
    location: string;
    logo_url?: string;
  };
}

const STATUS_COLORS = {
  applying: '#FFA500',
  applied: '#4CAF50',
  failed: '#F44336',
  interviewing: '#2196F3',
  offered: '#9C27B0',
  rejected: '#795548',
  accepted: '#4CAF50',
  withdrawn: '#607D8B',
};

const STATUS_OPTIONS = [
  { value: 'applying', label: 'Applying' },
  { value: 'applied', label: 'Applied' },
  { value: 'failed', label: 'Failed' },
  { value: 'interviewing', label: 'Interviewing' },
  { value: 'offered', label: 'Offered' },
  { value: 'rejected', label: 'Rejected' },
  { value: 'accepted', label: 'Accepted' },
  { value: 'withdrawn', label: 'Withdrawn' },
];

export default function AdminDashboard() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedApplications, setSelectedApplications] = useState<string[]>([]);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState('');
  const [statusNotes, setStatusNotes] = useState('');
  const [updating, setUpdating] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');

  useEffect(() => {
    loadApplications();
  }, []);

  const loadApplications = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const result = await getAdminApplications(user.id);
      
      if (result.success && result.data) {
        setApplications(result.data);
      } else {
        Alert.alert('Error', result.error || 'Failed to load applications');
      }
    } catch (error) {
      console.error('Error loading applications:', error);
      Alert.alert('Error', 'Failed to load applications');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadApplications();
    setRefreshing(false);
  };

  const toggleApplicationSelection = (appId: string) => {
    setSelectedApplications(prev => 
      prev.includes(appId) 
        ? prev.filter(id => id !== appId)
        : [...prev, appId]
    );
  };

  const selectAllApplications = () => {
    const filteredApps = getFilteredApplications();
    setSelectedApplications(filteredApps.map(app => app.id));
  };

  const clearSelection = () => {
    setSelectedApplications([]);
  };

  const handleStatusUpdate = async () => {
    if (!user?.id || !selectedStatus) return;

    try {
      setUpdating(true);
      
      if (selectedApplications.length === 1) {
        // Single update
        const result = await adminUpdateApplicationStatus(
          user.id,
          selectedApplications[0],
          selectedStatus,
          statusNotes
        );
        
        if (result.success) {
          Alert.alert('Success', result.message);
          await loadApplications();
        } else {
          Alert.alert('Error', result.error || 'Failed to update status');
        }
      } else {
        // Bulk update
        const result = await adminBulkUpdateApplicationStatus(
          user.id,
          selectedApplications,
          selectedStatus,
          statusNotes
        );
        
        if (result.success) {
          Alert.alert('Success', result.message);
          await loadApplications();
        } else {
          Alert.alert('Error', result.error || 'Failed to update statuses');
        }
      }
      
      setShowStatusModal(false);
      setSelectedApplications([]);
      setSelectedStatus('');
      setStatusNotes('');
    } catch (error) {
      console.error('Error updating status:', error);
      Alert.alert('Error', 'Failed to update status');
    } finally {
      setUpdating(false);
    }
  };

  const getFilteredApplications = () => {
    if (filterStatus === 'all') return applications;
    return applications.filter(app => app.status === filterStatus);
  };

  const getStatusColor = (status: string) => {
    return STATUS_COLORS[status as keyof typeof STATUS_COLORS] || '#666';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredApplications = getFilteredApplications();

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading applications...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Admin Dashboard</Text>
        <Text style={styles.subtitle}>Manage Application Statuses</Text>
      </View>

      {/* Filter and Actions Bar */}
      <View style={styles.actionBar}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterContainer}>
          <TouchableOpacity
            style={[styles.filterButton, filterStatus === 'all' && styles.filterButtonActive]}
            onPress={() => setFilterStatus('all')}
          >
            <Text style={[styles.filterButtonText, filterStatus === 'all' && styles.filterButtonTextActive]}>
              All ({applications.length})
            </Text>
          </TouchableOpacity>
          {STATUS_OPTIONS.map(status => {
            const count = applications.filter(app => app.status === status.value).length;
            return (
              <TouchableOpacity
                key={status.value}
                style={[styles.filterButton, filterStatus === status.value && styles.filterButtonActive]}
                onPress={() => setFilterStatus(status.value)}
              >
                <Text style={[styles.filterButtonText, filterStatus === status.value && styles.filterButtonTextActive]}>
                  {status.label} ({count})
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>

      {/* Selection Actions */}
      {selectedApplications.length > 0 && (
        <View style={styles.selectionBar}>
          <Text style={styles.selectionText}>
            {selectedApplications.length} selected
          </Text>
          <View style={styles.selectionActions}>
            <TouchableOpacity style={styles.actionButton} onPress={clearSelection}>
              <Text style={styles.actionButtonText}>Clear</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} onPress={selectAllApplications}>
              <Text style={styles.actionButtonText}>Select All</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.actionButton, styles.primaryActionButton]} 
              onPress={() => setShowStatusModal(true)}
            >
              <Text style={[styles.actionButtonText, styles.primaryActionButtonText]}>
                Update Status
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Applications List */}
      <ScrollView
        style={styles.applicationsList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {filteredApplications.map((app) => (
          <TouchableOpacity
            key={app.id}
            style={[
              styles.applicationCard,
              selectedApplications.includes(app.id) && styles.applicationCardSelected
            ]}
            onPress={() => toggleApplicationSelection(app.id)}
          >
            <View style={styles.applicationHeader}>
              <View style={styles.applicationInfo}>
                <Text style={styles.jobTitle}>{app.job_title || app.jobs?.title}</Text>
                <Text style={styles.company}>{app.job_company || app.jobs?.company}</Text>
                <Text style={styles.applicant}>
                  {app.profiles?.name} ({app.profiles?.email})
                </Text>
              </View>
              <View style={styles.statusContainer}>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(app.status) }]}>
                  <Text style={styles.statusText}>{app.status.toUpperCase()}</Text>
                </View>
                {selectedApplications.includes(app.id) && (
                  <FontAwesome name="check-circle" size={20} color="#007AFF" />
                )}
              </View>
            </View>
            
            <View style={styles.applicationFooter}>
              <Text style={styles.dateText}>Applied: {formatDate(app.created_at)}</Text>
              {app.updated_at !== app.created_at && (
                <Text style={styles.dateText}>Updated: {formatDate(app.updated_at)}</Text>
              )}
            </View>
            
            {app.admin_notes && (
              <View style={styles.notesContainer}>
                <Text style={styles.notesLabel}>Admin Notes:</Text>
                <Text style={styles.notesText}>{app.admin_notes}</Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
        
        {filteredApplications.length === 0 && (
          <View style={styles.emptyContainer}>
            <FontAwesome name="inbox" size={48} color="#ccc" />
            <Text style={styles.emptyText}>No applications found</Text>
          </View>
        )}
      </ScrollView>

      {/* Status Update Modal */}
      <Modal
        visible={showStatusModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowStatusModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Update Application Status</Text>
            <Text style={styles.modalSubtitle}>
              {selectedApplications.length} application(s) selected
            </Text>
            
            <Text style={styles.inputLabel}>New Status:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.statusOptions}>
              {STATUS_OPTIONS.map(status => (
                <TouchableOpacity
                  key={status.value}
                  style={[
                    styles.statusOption,
                    selectedStatus === status.value && styles.statusOptionSelected,
                    { borderColor: getStatusColor(status.value) }
                  ]}
                  onPress={() => setSelectedStatus(status.value)}
                >
                  <Text style={[
                    styles.statusOptionText,
                    selectedStatus === status.value && styles.statusOptionTextSelected
                  ]}>
                    {status.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            
            <Text style={styles.inputLabel}>Notes (optional):</Text>
            <TextInput
              style={styles.textInput}
              value={statusNotes}
              onChangeText={setStatusNotes}
              placeholder="Add notes about this status change..."
              multiline
              numberOfLines={3}
            />
            
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowStatusModal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleStatusUpdate}
                disabled={!selectedStatus || updating}
              >
                {updating ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.confirmButtonText}>Update</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 4,
  },
  actionBar: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filterContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  filterButtonActive: {
    backgroundColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  selectionBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#e3f2fd',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  selectionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1976d2',
  },
  selectionActions: {
    flexDirection: 'row',
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 8,
    borderRadius: 6,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  primaryActionButton: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  actionButtonText: {
    fontSize: 14,
    color: '#333',
  },
  primaryActionButtonText: {
    color: '#fff',
  },
  applicationsList: {
    flex: 1,
    padding: 16,
  },
  applicationCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  applicationCardSelected: {
    borderColor: '#007AFF',
    borderWidth: 2,
  },
  applicationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  applicationInfo: {
    flex: 1,
  },
  jobTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  company: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  applicant: {
    fontSize: 12,
    color: '#888',
    marginTop: 4,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#fff',
  },
  applicationFooter: {
    marginTop: 12,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  dateText: {
    fontSize: 12,
    color: '#888',
  },
  notesContainer: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#f9f9f9',
    borderRadius: 4,
  },
  notesLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#666',
  },
  notesText: {
    fontSize: 12,
    color: '#333',
    marginTop: 2,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    width: '90%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 4,
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  statusOptions: {
    marginBottom: 16,
  },
  statusOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 6,
    borderWidth: 1,
    backgroundColor: '#f9f9f9',
  },
  statusOptionSelected: {
    backgroundColor: '#e3f2fd',
  },
  statusOptionText: {
    fontSize: 14,
    color: '#666',
  },
  statusOptionTextSelected: {
    color: '#1976d2',
    fontWeight: '500',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 12,
    fontSize: 14,
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  confirmButton: {
    backgroundColor: '#007AFF',
    marginLeft: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666',
  },
  confirmButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
});
